#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分析脚本 - 用于处理BLE设备测试数据
功能：
1. 识别不同组合（探头+硅胶片+镜片）
2. 分类每组中的5台机器数据
3. 过滤"Got BLE CMD49"，提取G值和B值
4. 生成分类报告
"""

import re
import json
from collections import defaultdict
from typing import Dict, List, Tuple

class DataAnalyzer:
    def __init__(self, file_path: str):
        """
        初始化数据分析器
        
        Args:
            file_path: 输入文件路径
        """
        self.file_path = file_path
        self.groups = {}  # 存储所有组的数据
        self.current_group = None
        self.current_machine = None
        
    def parse_file(self) -> Dict:
        """
        解析输入文件，提取并分类数据
        
        Returns:
            包含所有分类数据的字典
        """
        with open(self.file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
                
            # 识别组标题（例如：第一组（探头（0xa） + 硅胶片（0xA） + 镜片1（0x1）））
            group_match = re.match(r'第(\w+)组（(.+)）', line)
            if group_match:
                group_name = group_match.group(1)
                group_config = group_match.group(2)
                self.current_group = f"第{group_name}组"
                self.groups[self.current_group] = {
                    'config': group_config,
                    'machines': {}
                }
                continue
            
            # 识别机器标题（例如：第一台：）
            machine_match = re.match(r'第(\w+)台：?', line)
            if machine_match and self.current_group:
                machine_name = f"第{machine_match.group(1)}台"
                self.current_machine = machine_name
                self.groups[self.current_group]['machines'][machine_name] = {
                    'data': [],
                    'g_values': [],
                    'b_values': []
                }
                continue
            
            # 跳过"Got BLE CMD49"行
            if "Got BLE CMD49" in line:
                continue
            
            # 提取测量数据（包含G值和B值）
            data_match = re.search(r'G:(\d+)\s+B:(\d+)', line)
            if data_match and self.current_group and self.current_machine:
                g_value = int(data_match.group(1))
                b_value = int(data_match.group(2))
                
                # 存储完整数据行
                self.groups[self.current_group]['machines'][self.current_machine]['data'].append(line)
                # 单独存储G值和B值用于统计分析
                self.groups[self.current_group]['machines'][self.current_machine]['g_values'].append(g_value)
                self.groups[self.current_group]['machines'][self.current_machine]['b_values'].append(b_value)
        
        return self.groups
    
    def calculate_statistics(self, values: List[int]) -> Dict:
        """
        计算数值列表的统计信息
        
        Args:
            values: 数值列表
            
        Returns:
            包含统计信息的字典
        """
        if not values:
            return {}
        
        return {
            '最小值': min(values),
            '最大值': max(values),
            '平均值': round(sum(values) / len(values), 2),
            '数据点数': len(values),
            '范围': max(values) - min(values)
        }
    
    def generate_report(self) -> str:
        """
        生成详细的分析报告
        
        Returns:
            格式化的报告字符串
        """
        report = []
        report.append("=" * 60)
        report.append("BLE设备测试数据分析报告")
        report.append("=" * 60)
        report.append("")
        
        for group_name, group_data in self.groups.items():
            report.append(f"📊 {group_name}")
            report.append(f"配置: {group_data['config']}")
            report.append("-" * 50)
            
            for machine_name, machine_data in group_data['machines'].items():
                report.append(f"  🔧 {machine_name}")
                
                # G值统计
                g_stats = self.calculate_statistics(machine_data['g_values'])
                if g_stats:
                    report.append(f"    G值统计: 最小={g_stats['最小值']}, 最大={g_stats['最大值']}, "
                                f"平均={g_stats['平均值']}, 范围={g_stats['范围']}")
                
                # B值统计
                b_stats = self.calculate_statistics(machine_data['b_values'])
                if b_stats:
                    report.append(f"    B值统计: 最小={b_stats['最小值']}, 最大={b_stats['最大值']}, "
                                f"平均={b_stats['平均值']}, 范围={b_stats['范围']}")
                
                report.append(f"    数据点数: {len(machine_data['data'])}")
                report.append("")
            
            report.append("")
        
        return "\n".join(report)
    
    def export_filtered_data(self, output_file: str):
        """
        导出过滤后的数据到文件
        
        Args:
            output_file: 输出文件路径
        """
        with open(output_file, 'w', encoding='utf-8') as file:
            file.write("过滤后的测试数据（已移除'Got BLE CMD49'）\n")
            file.write("=" * 60 + "\n\n")
            
            for group_name, group_data in self.groups.items():
                file.write(f"{group_name}\n")
                file.write(f"配置: {group_data['config']}\n\n")
                
                for machine_name, machine_data in group_data['machines'].items():
                    file.write(f"{machine_name}\n")
                    for data_line in machine_data['data']:
                        file.write(f"  {data_line}\n")
                    file.write("\n")
                
                file.write("\n")
    
    def export_gb_values_csv(self, output_file: str):
        """
        导出G值和B值到CSV文件
        
        Args:
            output_file: 输出CSV文件路径
        """
        import csv
        
        with open(output_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['组别', '机器', '序号', 'G值', 'B值'])
            
            for group_name, group_data in self.groups.items():
                for machine_name, machine_data in group_data['machines'].items():
                    g_values = machine_data['g_values']
                    b_values = machine_data['b_values']
                    
                    for i, (g, b) in enumerate(zip(g_values, b_values), 1):
                        writer.writerow([group_name, machine_name, i, g, b])

def main():
    """主函数"""
    print("🔍 开始分析BLE设备测试数据...")
    
    # 创建分析器实例
    analyzer = DataAnalyzer('测试.txt')
    
    # 解析文件
    print("📖 正在解析数据文件...")
    data = analyzer.parse_file()
    
    # 生成报告
    print("📊 正在生成分析报告...")
    report = analyzer.generate_report()
    print(report)
    
    # 导出过滤后的数据
    print("💾 正在导出过滤后的数据...")
    analyzer.export_filtered_data('filtered_data.txt')
    
    # 导出G值和B值到CSV
    print("📈 正在导出G值和B值到CSV...")
    analyzer.export_gb_values_csv('gb_values.csv')
    
    print("✅ 分析完成！")
    print("📁 生成的文件:")
    print("  - filtered_data.txt: 过滤后的完整数据")
    print("  - gb_values.csv: G值和B值的CSV格式数据")

if __name__ == "__main__":
    main()
